"use client"

import { useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { UserSelector } from "@/components/user-selector"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useCreateTask, useUpdateTask } from "@/hooks/use-tasks"
import { useProjectOptions } from "@/hooks/use-projects"
import { Loader2, Calendar, User, Flag, Folder } from "lucide-react"
import { toast } from "@/hooks/use-toast"

// Form validation schema
const taskFormSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title too long"),
  description: z.string().optional(),
  priority: z.enum(["low", "medium", "high", "urgent"]).default("medium"),
  due_date: z.string().optional(),
  estimated_hours: z.coerce.number().positive().optional(),
  project_id: z.string().optional(),
  assigned_to: z.string().optional(),
})

type TaskFormData = z.infer<typeof taskFormSchema>

interface Task {
  id: string
  title: string
  description?: string
  priority: "low" | "medium" | "high" | "urgent"
  due_date?: string
  estimated_hours?: number
  assigned_to?: string
  assigned_to_name?: string
}

interface EnhancedTaskModalProps {
  isOpen: boolean
  onClose: () => void
  task?: Task | null
  currentUserId?: string
}

export function EnhancedTaskModal({ isOpen, onClose, task, currentUserId }: EnhancedTaskModalProps) {
  const createTaskMutation = useCreateTask()
  const updateTaskMutation = useUpdateTask()
  const projectOptions = useProjectOptions()

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<TaskFormData>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: "",
      description: "",
      priority: "medium",
      due_date: "",
      estimated_hours: undefined,
      project_id: "",
    },
  })

  const isEditing = !!task

  // Reset form when modal opens/closes or task changes
  useEffect(() => {
    if (isOpen) {
      if (task) {
        // Editing existing task
        reset({
          title: task.title,
          description: task.description || "",
          priority: task.priority,
          due_date: task.due_date ? task.due_date.split('T')[0] : "", // Format for date input
          estimated_hours: task.estimated_hours,
        })
      } else {
        // Creating new task
        reset({
          title: "",
          description: "",
          priority: "medium",
          due_date: "",
          estimated_hours: undefined,
        })
      }
    }
  }, [isOpen, task, reset])

  const onSubmit = async (data: TaskFormData) => {
    try {
      const taskData = {
        title: data.title,
        description: data.description,
        priority: data.priority,
        due_date: data.due_date ? new Date(data.due_date).toISOString() : undefined,
        estimated_hours: data.estimated_hours,
        assigned_to: currentUserId, // Assign to current user by default
      }

      if (isEditing && task) {
        await updateTaskMutation.mutateAsync({ id: task.id, data: taskData })
      } else {
        await createTaskMutation.mutateAsync(taskData)
      }

      onClose()
    } catch (error) {
      // Error handling is done in the mutation hooks
      console.error("Task submission error:", error)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  const priorityColors = {
    low: "text-blue-600 bg-blue-50 border-blue-200",
    medium: "text-orange-600 bg-orange-50 border-orange-200",
    high: "text-red-600 bg-red-50 border-red-200",
    urgent: "text-purple-600 bg-purple-50 border-purple-200",
  }

  const watchedPriority = watch("priority")

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Flag className="h-5 w-5" />
            {isEditing ? "Edit Task" : "Create New Task"}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title" className="text-sm font-medium">
              Title *
            </Label>
            <Input
              id="title"
              {...register("title")}
              placeholder="Enter task title..."
              className={errors.title ? "border-red-500" : ""}
              disabled={isSubmitting}
            />
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium">
              Description
            </Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Enter task description..."
              rows={3}
              className="resize-none"
              disabled={isSubmitting}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          {/* Priority and Due Date Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Priority */}
            <div className="space-y-2">
              <Label htmlFor="priority" className="text-sm font-medium">
                Priority
              </Label>
              <Select
                value={watchedPriority}
                onValueChange={(value) => setValue("priority", value as any)}
                disabled={isSubmitting}
              >
                <SelectTrigger className={`${priorityColors[watchedPriority]} border`}>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low" className="text-blue-600">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                      Low
                    </div>
                  </SelectItem>
                  <SelectItem value="medium" className="text-orange-600">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                      Medium
                    </div>
                  </SelectItem>
                  <SelectItem value="high" className="text-red-600">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                      High
                    </div>
                  </SelectItem>
                  <SelectItem value="urgent" className="text-purple-600">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                      Urgent
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Due Date */}
            <div className="space-y-2">
              <Label htmlFor="due_date" className="text-sm font-medium flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                Due Date
              </Label>
              <Input
                id="due_date"
                type="date"
                {...register("due_date")}
                disabled={isSubmitting}
                min={new Date().toISOString().split('T')[0]} // Prevent past dates
              />
              {errors.due_date && (
                <p className="text-sm text-red-600">{errors.due_date.message}</p>
              )}
            </div>
          </div>

          {/* Project and Estimated Hours Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Project */}
            <div className="space-y-2">
              <Label htmlFor="project_id" className="text-sm font-medium flex items-center gap-1">
                <Folder className="h-4 w-4" />
                Project
              </Label>
              <Select
                value={watch("project_id") || ""}
                onValueChange={(value) => setValue("project_id", value || undefined)}
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select project (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No project</SelectItem>
                  {projectOptions.map((project) => (
                    <SelectItem key={project.value} value={project.value}>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: project.color }}
                        />
                        {project.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Estimated Hours */}
            <div className="space-y-2">
              <Label htmlFor="estimated_hours" className="text-sm font-medium">
                Estimated Hours
              </Label>
              <Input
                id="estimated_hours"
                type="number"
                step="0.5"
                min="0"
                max="999"
                {...register("estimated_hours")}
                placeholder="e.g., 8.5"
                disabled={isSubmitting}
              />
              {errors.estimated_hours && (
                <p className="text-sm text-red-600">{errors.estimated_hours.message}</p>
              )}
            </div>
          </div>

          {/* User Assignment */}
          <div className="space-y-2">
            <Label htmlFor="assigned_to" className="text-sm font-medium flex items-center gap-1">
              <User className="h-4 w-4" />
              Assign to
            </Label>
            <UserSelector
              value={watch("assigned_to")}
              onValueChange={(value) => setValue("assigned_to", value)}
              placeholder="Select assignee (optional)"
              disabled={isSubmitting}
              allowUnassigned={true}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[100px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {isEditing ? "Updating..." : "Creating..."}
                </>
              ) : (
                isEditing ? "Update Task" : "Create Task"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
