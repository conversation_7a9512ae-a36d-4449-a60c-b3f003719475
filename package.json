{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "db:generate": "drizzle-kit generate:pg", "db:migrate": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio", "db:seed": "tsx scripts/seed.ts", "db:seed:calendar": "tsx scripts/seed-calendar.ts", "db:seed:holidays": "tsx scripts/seed-holidays.ts", "db:seed:labor-laws": "tsx scripts/seed-labor-laws.ts", "db:seed:admin": "tsx scripts/seed-admin.ts", "db:backup": "tsx scripts/backup.ts", "db:restore": "tsx scripts/restore.ts", "docker:build": "docker build -t nepal-payroll .", "docker:run": "docker run -p 3000:3000 nepal-payroll", "docker:compose:up": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:compose:logs": "docker-compose logs -f", "deploy:vercel": "vercel --prod", "deploy:staging": "vercel", "analyze": "ANALYZE=true npm run build", "clean": "rm -rf .next out dist", "setup": "node scripts/setup-optional-deps.js", "setup:user-management": "node scripts/setup-user-management.js", "setup:basic-tables": "node scripts/setup-basic-tables.js", "create:sample-users": "node scripts/create-sample-users.js", "test:user-management": "node scripts/test-user-management.js", "postinstall": "echo 'Setup complete'", "prepare": "husky install || echo '<PERSON><PERSON> not configured'"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@neondatabase/serverless": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/supabase-js": "latest", "autoprefixer": "^10.4.20", "bcryptjs": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.6.0", "dotenv": "^17.2.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "14.2.16", "next-themes": "latest", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-is": "^19.1.0", "react-resizable-panels": "^2.1.7", "@tanstack/react-query": "^5.59.0", "@tanstack/react-query-devtools": "^5.59.0", "recharts": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}