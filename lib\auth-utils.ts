import bcrypt from "bcryptjs"
import { cookies } from "next/headers"
import { serverDb, generateSessionToken, type User } from "./server-db"

const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000 // 7 days

export interface AuthResult {
  success: boolean
  error?: string
  user?: User
  sessionToken?: string
}

export class AuthService {
  static async hashPassword(password: string): Promise<string> {
    try {
      if (!password) {
        throw new Error("Password is required")
      }

      return await bcrypt.hash(password, 12)
    } catch (error) {
      console.error("Error hashing password:", error)
      throw new Error("Failed to hash password")
    }
  }

  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      if (!password || !hash) {
        return false
      }

      return await bcrypt.compare(password, hash)
    } catch (error) {
      console.error("Error verifying password:", error)
      return false
    }
  }

  static async login(email: string, password: string): Promise<AuthResult> {
    try {
      if (!email || !password) {
        return { success: false, error: "Email and password are required" }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return { success: false, error: "Invalid email format" }
      }

      // Find user by email
      const user = await serverDb.getUserByEmail(email.toLowerCase().trim())
      if (!user) {
        return { success: false, error: "Invalid email or password" }
      }

      // Get password hash from database
      const result = await serverDb.sql`
        SELECT password_hash FROM users WHERE email = ${email.toLowerCase().trim()}
      `

      if (!result[0]) {
        return { success: false, error: "Invalid email or password" }
      }

      // Verify password
      const isValidPassword = await this.verifyPassword(password, result[0].password_hash)
      if (!isValidPassword) {
        return { success: false, error: "Invalid email or password" }
      }

      // Generate session token
      const sessionToken = generateSessionToken()
      const expiresAt = new Date(Date.now() + SESSION_DURATION)

      // Create session in database
      await serverDb.createSession(user.id, sessionToken, expiresAt)

      // Update last login
      await serverDb.updateUserLastLogin(user.id)

      return {
        success: true,
        user,
        sessionToken,
      }
    } catch (error) {
      console.error("Login error:", error)
      return { success: false, error: "An unexpected error occurred" }
    }
  }

  static async register(userData: {
    email: string
    password: string
    full_name: string
    role?: string
    department?: string
    position?: string
    phone?: string
    hire_date?: string
    employment_type?: string
    employment_status?: string
    salary?: number | string
    salary_currency?: string
    date_of_birth?: string
    gender?: string
    marital_status?: string
    nationality?: string
    citizenship_number?: string
    pan_number?: string
    emergency_contact_name?: string
    emergency_contact_phone?: string
    emergency_contact_relationship?: string
    bank_name?: string
    bank_account_number?: string
    bank_branch?: string
    is_active?: boolean
    email_verified?: boolean
  }): Promise<AuthResult> {
    try {
      if (!userData.email || !userData.password || !userData.full_name) {
        return { success: false, error: "Email, password, and full name are required" }
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(userData.email)) {
        return { success: false, error: "Invalid email format" }
      }

      // Validate password strength
      if (userData.password.length < 6) {
        return { success: false, error: "Password must be at least 6 characters long" }
      }

      // Check if user already exists
      const existingUser = await serverDb.getUserByEmail(userData.email.toLowerCase().trim())
      if (existingUser) {
        return { success: false, error: "User already exists with this email" }
      }

      // Hash password
      const passwordHash = await this.hashPassword(userData.password)

      // Create user
      const user = await serverDb.createUser({
        email: userData.email.toLowerCase().trim(),
        password_hash: passwordHash,
        full_name: userData.full_name.trim(),
        role: (userData.role as any) || "staff",
        department: userData.department?.trim(),
        position: userData.position?.trim(),
        phone: userData.phone?.trim(),
        hire_date: userData.hire_date || null,
        employment_type: userData.employment_type || 'full_time',
        employment_status: userData.employment_status || 'active',
        salary: userData.salary ? (typeof userData.salary === 'string' ? parseFloat(userData.salary) : userData.salary) : null,
        salary_currency: userData.salary_currency || 'NPR',
        date_of_birth: userData.date_of_birth || null,
        gender: userData.gender?.trim(),
        marital_status: userData.marital_status?.trim(),
        nationality: userData.nationality?.trim() || 'Nepali',
        citizenship_number: userData.citizenship_number?.trim(),
        pan_number: userData.pan_number?.trim(),
        emergency_contact_name: userData.emergency_contact_name?.trim(),
        emergency_contact_phone: userData.emergency_contact_phone?.trim(),
        emergency_contact_relationship: userData.emergency_contact_relationship?.trim(),
        bank_name: userData.bank_name?.trim(),
        bank_account_number: userData.bank_account_number?.trim(),
        bank_branch: userData.bank_branch?.trim(),
        is_active: userData.is_active !== undefined ? userData.is_active : true,
        email_verified: userData.email_verified !== undefined ? userData.email_verified : false,
      })

      return {
        success: true,
        user,
      }
    } catch (error) {
      console.error("Registration error:", error)
      if (error.message?.includes("already exists")) {
        return { success: false, error: "User already exists with this email" }
      }
      return { success: false, error: "An unexpected error occurred" }
    }
  }

  static async verifySession(sessionToken: string): Promise<User | null> {
    try {
      if (!sessionToken) {
        return null
      }

      const session = await serverDb.getSessionByToken(sessionToken)
      if (!session) {
        return null
      }

      const user = await serverDb.getUserById(session.user_id)
      return user
    } catch (error) {
      console.error("Session verification error:", error)
      return null
    }
  }

  static async logout(sessionToken: string): Promise<void> {
    try {
      if (sessionToken) {
        await serverDb.deleteSession(sessionToken)
      }
    } catch (error) {
      console.error("Logout error:", error)
    }
  }

  static async getCurrentUser(): Promise<User | null> {
    try {
      const cookieStore = await cookies()
      const sessionToken = cookieStore.get("session-token")?.value

      if (!sessionToken) {
        return null
      }

      return this.verifySession(sessionToken)
    } catch (error) {
      console.error("Get current user error:", error)
      return null
    }
  }

  static async cleanup(): Promise<void> {
    try {
      await serverDb.deleteExpiredSessions()
    } catch (error) {
      console.error("Cleanup error:", error)
    }
  }
}
