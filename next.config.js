/** @type {import('next').NextConfig} */

const nextConfig = {
  // Enable experimental features
  experimental: {
    // Enable server components
    serverComponentsExternalPackages: ['@neon-tech/serverless'],
    // Enable turbopack for faster builds
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
  },

  // TypeScript configuration
  typescript: {
    // Ignore build errors in production (handle via CI/CD)
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },

  // ESLint configuration
  eslint: {
    // Ignore lint errors during builds (handle via CI/CD)
    ignoreDuringBuilds: process.env.NODE_ENV === 'production',
  },

  // Image optimization
  images: {
    domains: ['localhost', 'your-domain.com'],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ],
      },
    ]
  },

  // Redirects for SEO and user experience
  async redirects() {
    return [
      {
        source: '/employee',
        destination: '/employee/dashboard',
        permanent: true,
      },
      {
        source: '/payroll',
        destination: '/admin/payroll',
        permanent: true,
      },
    ]
  },

  // Rewrites for API versioning
  async rewrites() {
    return [
      {
        source: '/api/v1/:path*',
        destination: '/api/:path*',
      },
    ]
  },

  // Environment variables to expose to the client
  env: {
    NEPAL_FISCAL_YEAR: process.env.NEPAL_FISCAL_YEAR,
    NEPAL_TIMEZONE: process.env.NEPAL_TIMEZONE,
    DEFAULT_CURRENCY: process.env.DEFAULT_CURRENCY,
    ENABLE_NEPAL_LOCALIZATION: process.env.ENABLE_NEPAL_LOCALIZATION,
    ENABLE_COMPLIANCE_MONITORING: process.env.ENABLE_COMPLIANCE_MONITORING,
    ENABLE_BULK_PROCESSING: process.env.ENABLE_BULK_PROCESSING,
  },

  // Webpack configuration
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Add custom webpack configurations
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname),
    }

    // Optimize bundle size
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: false,
          vendors: false,
          // Vendor chunk
          vendor: {
            name: 'vendor',
            chunks: 'all',
            test: /node_modules/,
            priority: 20,
          },
          // Common chunk
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 10,
            reuseExistingChunk: true,
            enforce: true,
          },
        },
      }
    }

    // Handle SVG imports
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    })

    return config
  },

  // Output configuration
  output: process.env.NODE_ENV === 'production' ? 'standalone' : undefined,

  // Compression
  compress: true,

  // Power by header
  poweredByHeader: false,

  // Generate ETags
  generateEtags: true,

  // Page extensions
  pageExtensions: ['ts', 'tsx', 'js', 'jsx', 'md', 'mdx'],

  // Trailing slash
  trailingSlash: false,

  // React strict mode
  reactStrictMode: true,

  // SWC minification
  swcMinify: true,

  // Bundle analyzer (enable with ANALYZE=true)
  ...(process.env.ANALYZE === 'true' && {
    webpack: (config, { isServer }) => {
      if (!isServer) {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: '../bundle-analyzer-report.html',
          })
        )
      }
      return config
    },
  }),

  // Logging configuration
  logging: {
    fetches: {
      fullUrl: process.env.NODE_ENV === 'development',
    },
  },

  // Server runtime configuration
  serverRuntimeConfig: {
    // Server-only configuration
    DATABASE_URL: process.env.DATABASE_URL,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    ENCRYPTION_KEY: process.env.ENCRYPTION_KEY,
    JWT_SECRET: process.env.JWT_SECRET,
  },

  // Public runtime configuration
  publicRuntimeConfig: {
    // Client-side configuration
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    NEPAL_FISCAL_YEAR: process.env.NEPAL_FISCAL_YEAR,
    DEFAULT_CURRENCY: process.env.DEFAULT_CURRENCY,
  },
}

// Sentry configuration for error tracking (optional)
let withSentryConfig = null
let sentryWebpackPluginOptions = {}

try {
  if (process.env.SENTRY_DSN) {
    const sentry = require('@sentry/nextjs')
    withSentryConfig = sentry.withSentryConfig
    sentryWebpackPluginOptions = {
      // Additional config options for the Sentry Webpack plugin
      silent: true,
      org: process.env.SENTRY_ORG,
      project: process.env.SENTRY_PROJECT,
      authToken: process.env.SENTRY_AUTH_TOKEN,
    }
  }
} catch (error) {
  console.warn('Sentry not available - continuing without error tracking')
}

// PWA configuration (optional)
let withPWA = null
try {
  if (process.env.ENABLE_PWA === 'true') {
    const nextPWA = require('next-pwa')
    withPWA = nextPWA({
      dest: 'public',
      register: true,
      skipWaiting: true,
      disable: process.env.NODE_ENV === 'development',
      runtimeCaching: [
        {
          urlPattern: /^https?.*/,
          handler: 'NetworkFirst',
          options: {
            cacheName: 'offlineCache',
            expiration: {
              maxEntries: 200,
              maxAgeSeconds: 24 * 60 * 60, // 24 hours
            },
          },
        },
      ],
    })
  }
} catch (error) {
  console.warn('PWA not available - continuing without PWA features')
}

// Bundle analyzer configuration (optional)
let withBundleAnalyzer = null
try {
  if (process.env.ANALYZE === 'true') {
    const bundleAnalyzer = require('@next/bundle-analyzer')
    withBundleAnalyzer = bundleAnalyzer({
      enabled: process.env.ANALYZE === 'true',
    })
  }
} catch (error) {
  console.warn('Bundle analyzer not available - continuing without analysis features')
}

// Compose all configurations
let config = nextConfig

// Apply PWA configuration if available
if (withPWA && process.env.ENABLE_PWA === 'true') {
  config = withPWA(config)
}

// Apply bundle analyzer if available
if (withBundleAnalyzer && process.env.ANALYZE === 'true') {
  config = withBundleAnalyzer(config)
}

// Apply Sentry configuration if available
if (withSentryConfig && process.env.SENTRY_DSN) {
  config = withSentryConfig(config, sentryWebpackPluginOptions)
}

module.exports = config
