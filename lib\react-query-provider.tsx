"use client"

import { useState } from "react"

// Temporary fallback until React Query is installed
let QueryClient: any
let QueryClientProvider: any
let ReactQueryDevtools: any

try {
  const reactQuery = require("@tanstack/react-query")
  QueryClient = reactQuery.QueryClient
  QueryClientProvider = reactQuery.QueryClientProvider

  try {
    const devtools = require("@tanstack/react-query-devtools")
    ReactQueryDevtools = devtools.ReactQueryDevtools
  } catch (e) {
    ReactQueryDevtools = () => null
  }
} catch (e) {
  // Fallback implementations
  QueryClient = class {
    constructor() {}
  }
  QueryClientProvider = ({ children }: { children: React.ReactNode }) => <>{children}</>
  ReactQueryDevtools = () => null
}

export function ReactQueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            gcTime: 10 * 60 * 1000, // 10 minutes
            retry: (failureCount, error: any) => {
              // Don't retry on 4xx errors
              if (error?.status >= 400 && error?.status < 500) {
                return false
              }
              return failureCount < 3
            },
          },
          mutations: {
            retry: false,
          },
        },
      })
  )

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  )
}
