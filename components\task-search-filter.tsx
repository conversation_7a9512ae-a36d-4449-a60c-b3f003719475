"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { UserSelector } from "@/components/user-selector"
import { useProjectOptions } from "@/hooks/use-projects"
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  Flag,
  User,
  Folder,
  SlidersHorizontal,
  Save,
  Bookmark
} from "lucide-react"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"

interface TaskFilters {
  search?: string
  status?: string
  priority?: string
  assigned_to?: string
  project_id?: string
  due_date_from?: string
  due_date_to?: string
}

interface TaskSearchFilterProps {
  filters: TaskFilters
  onFiltersChange: (filters: TaskFilters) => void
  className?: string
}

const statusOptions = [
  { value: "todo", label: "To Do", color: "bg-gray-500" },
  { value: "in_progress", label: "In Progress", color: "bg-blue-500" },
  { value: "completed", label: "Completed", color: "bg-green-500" },
  { value: "cancelled", label: "Cancelled", color: "bg-red-500" },
]

const priorityOptions = [
  { value: "low", label: "Low", color: "bg-blue-500" },
  { value: "medium", label: "Medium", color: "bg-orange-500" },
  { value: "high", label: "High", color: "bg-red-500" },
  { value: "urgent", label: "Urgent", color: "bg-purple-500" },
]

export function TaskSearchFilter({ filters, onFiltersChange, className = "" }: TaskSearchFilterProps) {
  const [searchValue, setSearchValue] = useState(filters.search || "")
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)
  const projectOptions = useProjectOptions()

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchValue !== filters.search) {
        onFiltersChange({ ...filters, search: searchValue || undefined })
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [searchValue, filters, onFiltersChange])

  const updateFilter = (key: keyof TaskFilters, value: string | undefined) => {
    onFiltersChange({
      ...filters,
      [key]: value || undefined,
    })
  }

  const clearAllFilters = () => {
    setSearchValue("")
    onFiltersChange({})
  }

  const getActiveFilterCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== "").length
  }

  const activeFilterCount = getActiveFilterCount()

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Bar */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search tasks by title or description..."
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="pl-10"
          />
        </div>
        
        {/* Advanced Filters Toggle */}
        <Popover open={isAdvancedOpen} onOpenChange={setIsAdvancedOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="relative">
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Filters
              {activeFilterCount > 0 && (
                <Badge 
                  variant="secondary" 
                  className="ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
                >
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Advanced Filters</h4>
                {activeFilterCount > 0 && (
                  <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                    Clear All
                  </Button>
                )}
              </div>

              <Separator />

              {/* Status Filter */}
              <div className="space-y-2">
                <Label className="flex items-center gap-1">
                  <Flag className="h-4 w-4" />
                  Status
                </Label>
                <Select
                  value={filters.status || ""}
                  onValueChange={(value) => updateFilter("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    {statusOptions.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${status.color}`} />
                          {status.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Priority Filter */}
              <div className="space-y-2">
                <Label className="flex items-center gap-1">
                  <Flag className="h-4 w-4" />
                  Priority
                </Label>
                <Select
                  value={filters.priority || ""}
                  onValueChange={(value) => updateFilter("priority", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All priorities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All priorities</SelectItem>
                    {priorityOptions.map((priority) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${priority.color}`} />
                          {priority.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Assignee Filter */}
              <div className="space-y-2">
                <Label className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  Assignee
                </Label>
                <UserSelector
                  value={filters.assigned_to}
                  onValueChange={(value) => updateFilter("assigned_to", value)}
                  placeholder="All assignees"
                  allowUnassigned={true}
                />
              </div>

              {/* Project Filter */}
              <div className="space-y-2">
                <Label className="flex items-center gap-1">
                  <Folder className="h-4 w-4" />
                  Project
                </Label>
                <Select
                  value={filters.project_id || ""}
                  onValueChange={(value) => updateFilter("project_id", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All projects" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All projects</SelectItem>
                    {projectOptions.map((project) => (
                      <SelectItem key={project.value} value={project.value}>
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: project.color }}
                          />
                          {project.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Due Date Range */}
              <div className="space-y-2">
                <Label className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Due Date Range
                </Label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs text-gray-500">From</Label>
                    <Input
                      type="date"
                      value={filters.due_date_from || ""}
                      onChange={(e) => updateFilter("due_date_from", e.target.value)}
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-gray-500">To</Label>
                    <Input
                      type="date"
                      value={filters.due_date_to || ""}
                      onChange={(e) => updateFilter("due_date_to", e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              Search: "{filters.search}"
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => {
                  setSearchValue("")
                  updateFilter("search", undefined)
                }}
              />
            </Badge>
          )}
          
          {filters.status && (
            <Badge variant="secondary" className="gap-1">
              Status: {statusOptions.find(s => s.value === filters.status)?.label}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilter("status", undefined)}
              />
            </Badge>
          )}
          
          {filters.priority && (
            <Badge variant="secondary" className="gap-1">
              Priority: {priorityOptions.find(p => p.value === filters.priority)?.label}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilter("priority", undefined)}
              />
            </Badge>
          )}
          
          {filters.project_id && (
            <Badge variant="secondary" className="gap-1">
              Project: {projectOptions.find(p => p.value === filters.project_id)?.label}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => updateFilter("project_id", undefined)}
              />
            </Badge>
          )}
          
          {(filters.due_date_from || filters.due_date_to) && (
            <Badge variant="secondary" className="gap-1">
              Due: {filters.due_date_from || "..."} to {filters.due_date_to || "..."}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => {
                  updateFilter("due_date_from", undefined)
                  updateFilter("due_date_to", undefined)
                }}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
